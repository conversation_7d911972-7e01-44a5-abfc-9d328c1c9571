#!/usr/bin/env python3
r"""
OLM ▶ PST Converter (single‑file, Windows 11)
============================================
Konvertiert Outlook‑für‑Mac‑Archive (*.olm) in Unicode‑PST **ohne** sichtbare Outlook‑Fenster.

Workflow pro OLM‑Datei
----------------------
1.  `olmConvert` extrahiert alle Nachrichten → *.eml*
2.  Unsichtbare `outlook.exe /embedding`‑Instanz erstellt eine neue PST
3.  Jede *.eml* wird via `CreateItemFromTemplate()` importiert und in Ordner **Imported** verschoben
4.  GUI zeigt den tatsächlichen Speicherort der PST

Alles lokal, 100 % Open Source – keine Cloud, keine externen SDKs.
"""

import ctypes
import shutil
import subprocess
import sys
import tempfile
import threading
import time
import traceback
import warnings
from datetime import datetime
from pathlib import Path

from PySide6.QtCore import Qt, QThread, Signal, QCoreApplication
from PySide6.QtWidgets import (
    QApplication,
    QFileDialog,
    QLabel,
    QListWidget,
    QListWidgetItem,
    QMainWindow,
    QMessageBox,
    QPushButton,
    QProgressBar,
    QVBoxLayout,
    QWidget,
)

import pythoncom  # pywin32 runtime
import win32com.client

# ---- OLM → EML helper ------------------------------------------------------
try:
    from olmConvert import convertOLM  # MIT‑Lizenz, neben diesem Script
except ImportError:
    raise SystemExit(
        "olmConvert.py fehlt – bitte aus https://github.com/PeterWarrington/olm-convert v2.1 herunterladen und neben dieses Skript legen."
    )

OL_STORE_UNICODE = 3
CREATE_NO_WINDOW = 0x08000000


# ---------------------------------------------------------------------------
# Hilfsfunktionen
# ---------------------------------------------------------------------------

def _set_dpi_aware():
    try:
        ctypes.windll.shcore.SetProcessDpiAwareness(1)
    except Exception:
        try:
            ctypes.windll.user32.SetProcessDPIAware()
        except Exception:
            pass


def _launch_outlook_headless():
    try:
        subprocess.Popen(
            ["outlook.exe", "/embedding", "/recycle"],
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            creationflags=CREATE_NO_WINDOW,
        )
        time.sleep(2)
    except FileNotFoundError:
        pass


# ---------------------------------------------------------------------------
# Worker‑Thread
# ---------------------------------------------------------------------------

class ConverterThread(QThread):
    overallProgress = Signal(int)
    currentFile = Signal(str)
    finishedWithErrors = Signal(str)
    pstCreated = Signal(str)

    def __init__(self, olm_paths):
        super().__init__()
        self._olm_paths = list(olm_paths)
        self._stop = threading.Event()
        self._outlook = None

    def run(self):
        pythoncom.CoInitialize()
        try:
            self._outlook = self._ensure_outlook()
            if self._outlook is None:
                return
            mapi = self._outlook.GetNamespace("MAPI")
            total = len(self._olm_paths)
            for idx, olm in enumerate(self._olm_paths, 1):
                if self._stop.is_set():
                    break
                olm_path = Path(olm)
                self.currentFile.emit(olm_path.name)
                try:
                    pst_path = self._convert_olm(olm_path, mapi)
                    self.pstCreated.emit(str(pst_path))
                except Exception as exc:
                    self.finishedWithErrors.emit(
                        f"{olm_path.name}: {exc}\n{traceback.format_exc()}")
                self.overallProgress.emit(int(idx / total * 100))
        finally:
            try:
                if self._outlook:
                    self._outlook.Quit()
            except Exception:
                pass
            pythoncom.CoUninitialize()

    # ------------------------------------------------------------------
    def _ensure_outlook(self):
        for attempt in (0, 1):
            try:
                return win32com.client.Dispatch("Outlook.Application")
            except pythoncom.com_error:
                if attempt == 0:
                    _launch_outlook_headless()
                else:
                    self.finishedWithErrors.emit(
                        "Outlook‑COM konnte nicht gestartet werden. Klassische Desktop‑Version fehlt oder Bitness passt nicht."
                    )
                    return None

    # ------------------------------------------------------------------
    def _convert_olm(self, olm_path: Path, mapi):
        tmp_dir = Path(tempfile.mkdtemp(prefix="olm_tmp_"))
        try:
            convertOLM(str(olm_path), str(tmp_dir),
                       noAttachments=False, format="eml")
            timestamp = datetime.now().strftime("_%Y%m%d_%H%M%S")
            desired_pst = olm_path.with_stem(
                olm_path.stem + timestamp).with_suffix(".pst")
            mapi.AddStoreEx(str(desired_pst), OL_STORE_UNICODE)
            new_store = mapi.Folders.Item(mapi.Folders.Count)
            inbox = new_store.Folders.Add("Imported")
            for eml in tmp_dir.rglob("*.eml"):
                if self._stop.is_set():
                    break
                try:
                    # Outlook scheitert bei langen Pfaden / Nicht‑ASCII ⇒ temporäre Kopie mit kurzem ASCII‑Namen
                    short_eml = tmp_dir / ("_x.eml")
                    shutil.copy2(eml, short_eml)
                    mail = self._outlook.CreateItemFromTemplate(str(short_eml))
                    mail.Move(inbox)
                    short_eml.unlink(missing_ok=True)
                except Exception as e:
                    print("Skip:", eml.name, e)
            mapi.RemoveStore(new_store)
        finally:
            shutil.rmtree(tmp_dir, ignore_errors=True)
        if desired_pst.exists():
            return desired_pst
        fb = Path.home() / "Documents" / "Outlook‑Dateien" / desired_pst.name
        if fb.exists():
            return fb
        raise FileNotFoundError(
            "Outlook hat die PST nicht gespeichert – Speicherort unbekannt.")


# ---------------------------------------------------------------------------
# GUI
# ---------------------------------------------------------------------------

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("OLM → PST Converter")
        self.resize(650, 420)
        self._had_error = False
        self.list_widget = QListWidget()
        self.add_btn = QPushButton("Dateien hinzufügen…")
        self.remove_btn = QPushButton("Markierte entfernen")
        self.start_btn = QPushButton("Konvertierung starten")
        self.progress = QProgressBar(maximum=100)
        self.status = QLabel("Bereit")
        layout = QVBoxLayout()
        for w in (self.list_widget, self.add_btn, self.remove_btn, self.start_btn, self.progress, self.status):
            layout.addWidget(w)
        container = QWidget()
        container.setLayout(layout)
        self.setCentralWidget(container)
        self.add_btn.clicked.connect(self._select_files)
        self.remove_btn.clicked.connect(self._remove_selected)
        self.start_btn.clicked.connect(self._start_conversion)

    def _select_files(self):
        files, _ = QFileDialog.getOpenFileNames(
            self, "OLM‑Dateien wählen", "", "Outlook for Mac‑Archive (*.olm)")
        for f in files:
            if not any(f == self.list_widget.item(i).text() for i in range(self.list_widget.count())):
                self.list_widget.addItem(QListWidgetItem(f))

    def _remove_selected(self):
        for itm in self.list_widget.selectedItems():
            self.list_widget.takeItem(self.list_widget.row(itm))

    def _start_conversion(self):
        if self.list_widget.count() == 0:
            QMessageBox.warning(
                self, "Keine Dateien", "Bitte zuerst mindestens eine .olm‑Datei hinzufügen.")
            return
        paths = [self.list_widget.item(i).text()
                 for i in range(self.list_widget.count())]
        self.progress.setValue(0)
        self.status.setText("Starte…")
        self.start_btn.setEnabled(False)
        self._had_error = False
        self.worker = ConverterThread(paths)
        self.worker.overallProgress.connect(self.progress.setValue)
        self.worker.currentFile.connect(
            lambda n: self.status.setText(f"Verarbeite: {n}"))
        self.worker.finishedWithErrors.connect(self._on_error)
        self.worker.pstCreated.connect(self._on_pst)
        self.worker.finished.connect(self._on_finish)
        self.worker.start()

    def _on_pst(self, path):
        self.status.setText(f"Erstellt: {path}")
        QMessageBox.information(
            self, "PST erstellt", f"Die PST‑Datei wurde gespeichert unter:\n{path}")

    def _on_error(self, msg):
        self._had_error = True
        self.status.setText("Fehler")
        QMessageBox.critical(self, "Fehler während der Konvertierung", msg)

    def _on_finish(self):
        self.status.setText("Fertig!")
        self.start_btn.setEnabled(True)
        if not self._had_error:
            QMessageBox.information(self, "Konvertierung abgeschlossen",
                                    "Alle ausgewählten OLM‑Dateien wurden erfolgreich konvertiert.")


# ---------------------------------------------------------------------------
# Entry‑Point
# ---------------------------------------------------------------------------

def main():
    warnings.simplefilter("default")
    _set_dpi_aware()
    QCoreApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app = QApplication(sys.argv)
    wnd = MainWindow()
    wnd.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
