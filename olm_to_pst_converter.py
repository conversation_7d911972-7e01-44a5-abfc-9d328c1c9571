#!/usr/bin/env python3
r"""
OLM ▶ PST Converter (single‑file, Windows 11)
============================================
Konvertiert Outlook‑für‑Mac‑Archive (*.olm) in Unicode‑PST **ohne** sichtbare Outlook‑Fenster.

Workflow pro OLM‑Datei
----------------------
1.  `olmConvert` extrahiert alle Nachrichten → *.eml*
2.  Unsichtbare `outlook.exe /embedding`‑Instanz erstellt eine neue PST
3.  Jede *.eml* wird via `CreateItemFromTemplate()` importiert und in Ordner **Imported** verschoben
4.  GUI zeigt den tatsächlichen Speicherort der PST

Alles lokal, 100 % Open Source – keine Cloud, keine externen SDKs.
"""

import ctypes
import email
import email.header
import email.utils
import html
import os
import re
import shutil
import subprocess
import sys
import tempfile
import threading
import time
import traceback
import warnings
from datetime import datetime
from pathlib import Path

from PySide6.QtCore import Qt, QThread, Signal, QCoreApplication
from PySide6.QtWidgets import (
    QApplication,
    QFileDialog,
    QLabel,
    QListWidget,
    QListWidgetItem,
    QMainWindow,
    QMessageBox,
    QPushButton,
    QProgressBar,
    QVBoxLayout,
    QWidget,
)

import pythoncom  # pywin32 runtime - für PST-Erstellung
import win32com.client

# ---- OLM → EML helper ------------------------------------------------------
try:
    from olmConvert import convertOLM  # MIT‑Lizenz, neben diesem Script
except ImportError:
    raise SystemExit(
        "olmConvert.py fehlt – bitte aus https://github.com/PeterWarrington/olm-convert v2.1 herunterladen und neben dieses Skript legen."
    )

OL_STORE_UNICODE = 3
CREATE_NO_WINDOW = 0x08000000


# ---------------------------------------------------------------------------
# Hilfsfunktionen
# ---------------------------------------------------------------------------

def _set_dpi_aware():
    try:
        ctypes.windll.shcore.SetProcessDpiAwareness(1)
    except Exception:
        try:
            ctypes.windll.user32.SetProcessDPIAware()
        except Exception:
            pass


def _launch_outlook_headless():
    """Startet Outlook im Hintergrund für PST-Operationen."""
    try:
        subprocess.Popen(
            ["outlook.exe", "/embedding", "/recycle"],
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            creationflags=subprocess.CREATE_NO_WINDOW,
        )
        time.sleep(3)  # Wartezeit für Outlook-Start
    except FileNotFoundError:
        pass


def create_pst_from_eml_folder(eml_folder_path, pst_output_path=None):
    """
    Erstellt eine PST-Datei aus einem Ordner mit .eml-Dateien.

    Args:
        eml_folder_path: Pfad zum Ordner mit .eml-Dateien
        pst_output_path: Gewünschter Pfad für die PST-Datei (optional)

    Returns:
        Pfad zur erstellten PST-Datei
    """
    eml_folder = Path(eml_folder_path)
    if not eml_folder.exists():
        raise FileNotFoundError(
            f"EML-Ordner nicht gefunden: {eml_folder_path}")

    # Sammle alle .eml-Dateien
    eml_files = list(eml_folder.glob("*.eml"))
    if not eml_files:
        raise ValueError(
            f"Keine .eml-Dateien im Ordner gefunden: {eml_folder_path}")

    # Bestimme PST-Ausgabepfad
    if pst_output_path is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        pst_output_path = eml_folder.parent / \
            f"{eml_folder.name}_{timestamp}.pst"
    else:
        pst_output_path = Path(pst_output_path)

    print(f"Erstelle PST-Datei: {pst_output_path}")
    print(f"Aus {len(eml_files)} .eml-Dateien")

    # Initialisiere COM
    pythoncom.CoInitialize()
    try:
        # Starte Outlook falls nötig
        outlook = None
        for attempt in range(3):
            try:
                outlook = win32com.client.Dispatch("Outlook.Application")
                _ = outlook.GetNamespace("MAPI")  # Test der Verbindung
                break
            except pythoncom.com_error as e:
                print(
                    f"Outlook-Verbindungsversuch {attempt + 1} fehlgeschlagen: {e}")
                if attempt < 2:
                    _launch_outlook_headless()
                    time.sleep(2)
                else:
                    raise Exception(
                        f"Outlook konnte nicht gestartet werden: {e}")

        if outlook is None:
            raise Exception("Outlook-Verbindung fehlgeschlagen")

        mapi = outlook.GetNamespace("MAPI")

        # Erstelle neue PST-Datei
        OL_STORE_UNICODE = 2
        mapi.AddStoreEx(str(pst_output_path), OL_STORE_UNICODE)
        new_store = mapi.Folders.Item(mapi.Folders.Count)

        # Erstelle Hauptordner
        inbox = new_store.Folders.Add("Imported Emails")

        successful_imports = 0
        failed_imports = 0

        print("Importiere .eml-Dateien...")
        for idx, eml_file in enumerate(eml_files, 1):
            try:
                # Lade .eml-Datei in Outlook
                mail = outlook.CreateItem(0)  # 0 = olMailItem

                # Outlook kann .eml-Dateien direkt laden
                # Aber wir müssen einen Umweg über eine temporäre .msg-Datei gehen
                # oder die .eml-Datei manuell parsen

                # Lese und parse die .eml-Datei
                with open(eml_file, 'rb') as f:
                    eml_content = f.read()

                msg = email.message_from_bytes(eml_content)

                # Extrahiere Betreff
                subject = "Imported Email"
                if msg['Subject']:
                    decoded_subject = email.header.decode_header(
                        msg['Subject'])
                    subject_parts = []
                    for part, encoding in decoded_subject:
                        if isinstance(part, bytes):
                            if encoding:
                                try:
                                    part = part.decode(encoding)
                                except (UnicodeDecodeError, LookupError):
                                    part = part.decode(
                                        'utf-8', errors='ignore')
                            else:
                                part = part.decode('utf-8', errors='ignore')
                        subject_parts.append(part)
                    subject = ''.join(subject_parts)

                # Setze Betreff
                mail.Subject = subject[:255]

                # Extrahiere Body
                body = ""
                if msg.is_multipart():
                    for part in msg.walk():
                        if part.get_content_type() == "text/plain":
                            payload = part.get_payload(decode=True)
                            if payload:
                                charset = part.get_content_charset() or 'utf-8'
                                try:
                                    body = payload.decode(charset)
                                except (UnicodeDecodeError, LookupError):
                                    body = payload.decode(
                                        'utf-8', errors='ignore')
                            break
                        elif part.get_content_type() == "text/html" and not body:
                            payload = part.get_payload(decode=True)
                            if payload:
                                charset = part.get_content_charset() or 'utf-8'
                                try:
                                    html_content = payload.decode(charset)
                                except (UnicodeDecodeError, LookupError):
                                    html_content = payload.decode(
                                        'utf-8', errors='ignore')
                                body = _html_to_text(html_content)
                else:
                    payload = msg.get_payload(decode=True)
                    if payload:
                        charset = msg.get_content_charset() or 'utf-8'
                        try:
                            content = payload.decode(charset)
                        except (UnicodeDecodeError, LookupError):
                            content = payload.decode('utf-8', errors='ignore')

                        if msg.get_content_type() == "text/html":
                            body = _html_to_text(content)
                        else:
                            body = content

                mail.Body = body

                # Extrahiere Absender
                if msg['From']:
                    from_header = msg['From']
                    decoded_from = email.header.decode_header(from_header)
                    from_parts = []
                    for part, encoding in decoded_from:
                        if isinstance(part, bytes):
                            if encoding:
                                try:
                                    part = part.decode(encoding)
                                except (UnicodeDecodeError, LookupError):
                                    part = part.decode(
                                        'utf-8', errors='ignore')
                            else:
                                part = part.decode('utf-8', errors='ignore')
                        from_parts.append(part)
                    from_decoded = ''.join(from_parts)

                    # Setze Absender
                    try:
                        if '<' in from_decoded and '>' in from_decoded:
                            sender_name = from_decoded.split(
                                '<')[0].strip().strip('"')
                            sender_email = from_decoded.split(
                                '<')[1].split('>')[0].strip()
                            mail.SenderEmailAddress = sender_email
                            mail.SenderName = sender_name
                        else:
                            mail.SenderEmailAddress = from_decoded.strip()
                            mail.SenderName = from_decoded.strip()
                    except Exception:
                        pass

                # Extrahiere Empfänger
                if msg['To']:
                    to_header = msg['To']
                    decoded_to = email.header.decode_header(to_header)
                    to_parts = []
                    for part, encoding in decoded_to:
                        if isinstance(part, bytes):
                            if encoding:
                                try:
                                    part = part.decode(encoding)
                                except (UnicodeDecodeError, LookupError):
                                    part = part.decode(
                                        'utf-8', errors='ignore')
                            else:
                                part = part.decode('utf-8', errors='ignore')
                        to_parts.append(part)
                    mail.To = ''.join(to_parts)

                # Extrahiere Datum
                if msg['Date']:
                    try:
                        sent_date = email.utils.parsedate_to_datetime(
                            msg['Date'])
                        mail.SentOn = sent_date
                    except Exception:
                        pass

                # Speichere E-Mail
                mail.Save()
                mail.Move(inbox)
                successful_imports += 1

                if idx % 10 == 0:
                    print(
                        f"Fortschritt: {idx}/{len(eml_files)} E-Mails importiert")

            except Exception as e:
                failed_imports += 1
                print(f"Fehler beim Importieren von {eml_file.name}: {e}")

        print(
            f"Import abgeschlossen: {successful_imports} erfolgreich, {failed_imports} fehlgeschlagen")

        # Schließe PST-Datei
        mapi.RemoveStore(new_store)

        return pst_output_path

    finally:
        try:
            if outlook:
                outlook.Quit()
        except Exception:
            pass
        pythoncom.CoUninitialize()


def _sanitize_filename(filename):
    """Entfernt problematische Zeichen aus Dateinamen für Windows."""
    # Entferne oder ersetze problematische Zeichen
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # Entferne führende/nachfolgende Leerzeichen und Punkte
    sanitized = sanitized.strip(' .')
    # Begrenze Länge auf 100 Zeichen
    if len(sanitized) > 100:
        sanitized = sanitized[:100]
    return sanitized if sanitized else "email"


def _html_to_text(html_content):
    """Konvertiert HTML-Inhalt zu lesbarem Text."""
    if not html_content:
        return ""

    text = html_content

    # Entferne komplette XML/HTML-Strukturen die von Microsoft Word kommen
    text = re.sub(r'<\?xml[^>]*\?>', '', text, flags=re.IGNORECASE)
    text = re.sub(r'<!DOCTYPE[^>]*>', '', text,
                  flags=re.IGNORECASE | re.DOTALL)
    text = re.sub(r'<html[^>]*xmlns[^>]*>', '', text, flags=re.IGNORECASE)
    text = re.sub(r'</html>', '', text, flags=re.IGNORECASE)

    # Entferne komplette head-Bereiche mit allem Inhalt
    text = re.sub(r'<head[^>]*>.*?</head>', '', text,
                  flags=re.IGNORECASE | re.DOTALL)
    text = re.sub(r'<style[^>]*>.*?</style>', '', text,
                  flags=re.IGNORECASE | re.DOTALL)
    text = re.sub(r'<script[^>]*>.*?</script>', '',
                  text, flags=re.IGNORECASE | re.DOTALL)

    # Entferne Microsoft Office XML-Namespaces und deren Inhalte
    text = re.sub(r'<o:[^>]*>.*?</o:[^>]*>', '', text,
                  flags=re.IGNORECASE | re.DOTALL)
    text = re.sub(r'<w:[^>]*>.*?</w:[^>]*>', '', text,
                  flags=re.IGNORECASE | re.DOTALL)
    text = re.sub(r'<v:[^>]*>.*?</v:[^>]*>', '', text,
                  flags=re.IGNORECASE | re.DOTALL)
    text = re.sub(r'<o:[^>]*>', '', text, flags=re.IGNORECASE)
    text = re.sub(r'</o:[^>]*>', '', text, flags=re.IGNORECASE)
    text = re.sub(r'<w:[^>]*>', '', text, flags=re.IGNORECASE)
    text = re.sub(r'</w:[^>]*>', '', text, flags=re.IGNORECASE)
    text = re.sub(r'<v:[^>]*>', '', text, flags=re.IGNORECASE)
    text = re.sub(r'</v:[^>]*>', '', text, flags=re.IGNORECASE)

    # Entferne spezielle Microsoft Word Strukturen
    text = re.sub(r'<meta[^>]*>', '', text, flags=re.IGNORECASE)
    text = re.sub(r'<!--[^>]*-->', '', text, flags=re.IGNORECASE | re.DOTALL)

    # Suche nach dem body-Tag und extrahiere nur dessen Inhalt
    body_match = re.search(
        r'<body[^>]*>(.*?)</body>', text, flags=re.IGNORECASE | re.DOTALL)
    if body_match:
        text = body_match.group(1)

    # Ersetze Block-Level-Elemente mit Zeilenumbrüchen
    text = re.sub(
        r'</?(?:p|div|h[1-6]|li|tr|br)\s*[^>]*>', '\n', text, flags=re.IGNORECASE)
    text = re.sub(
        r'</?(?:ul|ol|table|tbody|thead|tfoot|td|th)\s*[^>]*>', '\n', text, flags=re.IGNORECASE)

    # Entferne alle anderen HTML-Tags
    text = re.sub(r'<[^>]+>', '', text)

    # Dekodiere HTML-Entities
    text = html.unescape(text)

    # Aggressive Whitespace-Bereinigung
    # Reduziere mehrfache Leerzeilen
    text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)
    text = re.sub(r'[ \t]+', ' ', text)  # Reduziere mehrfache Leerzeichen
    # Entferne führende Leerzeichen pro Zeile
    text = re.sub(r'^\s+', '', text, flags=re.MULTILINE)
    # Entferne nachfolgende Leerzeichen pro Zeile
    text = re.sub(r'\s+$', '', text, flags=re.MULTILINE)

    # Entferne führende/nachfolgende Leerzeichen
    text = text.strip()

    # Falls der Text immer noch hauptsächlich HTML-Tags enthält, gib eine Warnung zurück
    if len(re.findall(r'<[^>]+>', text)) > len(text.split()) / 4:
        return "[WARNUNG: HTML-Konvertierung unvollständig]\n\n" + text

    return text


# ---------------------------------------------------------------------------
# Worker‑Thread
# ---------------------------------------------------------------------------

class PSTCreatorThread(QThread):
    """Thread für die Erstellung einer PST-Datei aus EML-Dateien."""
    progress = Signal(int)
    status = Signal(str)
    finished_success = Signal(str)  # PST-Pfad
    finished_error = Signal(str)    # Fehlermeldung

    def __init__(self, eml_folder_path, pst_output_path):
        super().__init__()
        self.eml_folder_path = eml_folder_path
        self.pst_output_path = pst_output_path

    def run(self):
        try:
            self.status.emit("Starte PST-Erstellung...")
            self.progress.emit(10)

            # Verwende eine angepasste Version der create_pst_from_eml_folder Funktion
            pst_path = self._create_pst_with_progress()

            self.progress.emit(100)
            self.finished_success.emit(str(pst_path))

        except Exception as e:
            error_msg = f"Fehler bei PST-Erstellung: {e}\n{traceback.format_exc()}"
            self.finished_error.emit(error_msg)

    def _create_pst_with_progress(self):
        """Erstellt PST-Datei mit Progress-Updates."""
        eml_folder = Path(self.eml_folder_path)
        if not eml_folder.exists():
            raise FileNotFoundError(
                f"EML-Ordner nicht gefunden: {self.eml_folder_path}")

        # Sammle alle .eml-Dateien
        eml_files = list(eml_folder.glob("*.eml"))
        if not eml_files:
            raise ValueError(
                f"Keine .eml-Dateien im Ordner gefunden: {self.eml_folder_path}")

        pst_output_path = Path(self.pst_output_path)

        self.status.emit(f"Gefunden: {len(eml_files)} .eml-Dateien")
        self.progress.emit(20)

        # Initialisiere COM
        pythoncom.CoInitialize()
        try:
            # Starte Outlook falls nötig
            self.status.emit("Verbinde mit Outlook...")
            outlook = None
            for attempt in range(3):
                try:
                    outlook = win32com.client.Dispatch("Outlook.Application")
                    _ = outlook.GetNamespace("MAPI")  # Test der Verbindung
                    break
                except pythoncom.com_error as e:
                    self.status.emit(
                        f"Outlook-Verbindungsversuch {attempt + 1}...")
                    if attempt < 2:
                        _launch_outlook_headless()
                        time.sleep(2)
                    else:
                        raise Exception(
                            f"Outlook konnte nicht gestartet werden: {e}")

            if outlook is None:
                raise Exception("Outlook-Verbindung fehlgeschlagen")

            self.progress.emit(30)
            mapi = outlook.GetNamespace("MAPI")

            # Erstelle neue PST-Datei
            self.status.emit("Erstelle PST-Datei...")
            OL_STORE_UNICODE = 2
            mapi.AddStoreEx(str(pst_output_path), OL_STORE_UNICODE)
            new_store = mapi.Folders.Item(mapi.Folders.Count)

            # Erstelle Hauptordner
            inbox = new_store.Folders.Add("Imported Emails")

            self.progress.emit(40)

            successful_imports = 0
            failed_imports = 0

            self.status.emit("Importiere E-Mails...")
            for idx, eml_file in enumerate(eml_files, 1):
                try:
                    # Lade .eml-Datei in Outlook
                    mail = outlook.CreateItem(0)  # 0 = olMailItem

                    # Lese und parse die .eml-Datei
                    with open(eml_file, 'rb') as f:
                        eml_content = f.read()

                    msg = email.message_from_bytes(eml_content)

                    # Extrahiere Betreff
                    subject = "Imported Email"
                    if msg['Subject']:
                        decoded_subject = email.header.decode_header(
                            msg['Subject'])
                        subject_parts = []
                        for part, encoding in decoded_subject:
                            if isinstance(part, bytes):
                                if encoding:
                                    try:
                                        part = part.decode(encoding)
                                    except (UnicodeDecodeError, LookupError):
                                        part = part.decode(
                                            'utf-8', errors='ignore')
                                else:
                                    part = part.decode(
                                        'utf-8', errors='ignore')
                            subject_parts.append(part)
                        subject = ''.join(subject_parts)

                    # Setze Betreff
                    mail.Subject = subject[:255]

                    # Extrahiere Body und Anhänge
                    body = ""
                    attachments = []

                    if msg.is_multipart():
                        for part in msg.walk():
                            content_type = part.get_content_type()
                            content_disposition = part.get_content_disposition()

                            # Text-Inhalt
                            if content_type == "text/plain" and not body:
                                payload = part.get_payload(decode=True)
                                if payload:
                                    charset = part.get_content_charset() or 'utf-8'
                                    try:
                                        body = payload.decode(charset)
                                    except (UnicodeDecodeError, LookupError):
                                        body = payload.decode(
                                            'utf-8', errors='ignore')
                            elif content_type == "text/html" and not body:
                                payload = part.get_payload(decode=True)
                                if payload:
                                    charset = part.get_content_charset() or 'utf-8'
                                    try:
                                        html_content = payload.decode(charset)
                                    except (UnicodeDecodeError, LookupError):
                                        html_content = payload.decode(
                                            'utf-8', errors='ignore')
                                    body = _html_to_text(html_content)

                            # Anhänge
                            elif content_disposition == 'attachment':
                                filename = part.get_filename()
                                if filename:
                                    # Dekodiere Dateiname
                                    decoded_filename = email.header.decode_header(
                                        filename)
                                    filename_parts = []
                                    for part_text, encoding in decoded_filename:
                                        if isinstance(part_text, bytes):
                                            if encoding:
                                                try:
                                                    part_text = part_text.decode(
                                                        encoding)
                                                except (UnicodeDecodeError, LookupError):
                                                    part_text = part_text.decode(
                                                        'utf-8', errors='ignore')
                                            else:
                                                part_text = part_text.decode(
                                                    'utf-8', errors='ignore')
                                        filename_parts.append(part_text)
                                    clean_filename = ''.join(filename_parts)

                                    # Speichere Anhang-Daten
                                    payload = part.get_payload(decode=True)
                                    if payload:
                                        attachments.append(
                                            (clean_filename, payload))
                    else:
                        # Einfache E-Mail
                        payload = msg.get_payload(decode=True)
                        if payload:
                            charset = msg.get_content_charset() or 'utf-8'
                            try:
                                content = payload.decode(charset)
                            except (UnicodeDecodeError, LookupError):
                                content = payload.decode(
                                    'utf-8', errors='ignore')

                            if msg.get_content_type() == "text/html":
                                body = _html_to_text(content)
                            else:
                                body = content

                    # Setze Body - verwende HTML wenn verfügbar für bessere Darstellung
                    if msg.is_multipart():
                        # Suche nach HTML-Version für bessere Darstellung
                        html_body = ""
                        for part in msg.walk():
                            if part.get_content_type() == "text/html":
                                payload = part.get_payload(decode=True)
                                if payload:
                                    charset = part.get_content_charset() or 'utf-8'
                                    try:
                                        html_body = payload.decode(charset)
                                    except (UnicodeDecodeError, LookupError):
                                        html_body = payload.decode(
                                            'utf-8', errors='ignore')
                                break

                        if html_body:
                            # Verwende HTML-Body für bessere Darstellung
                            mail.HTMLBody = html_body
                            mail.Body = body  # Fallback Text
                        else:
                            mail.Body = body
                    else:
                        if msg.get_content_type() == "text/html":
                            # Einfache HTML-E-Mail
                            payload = msg.get_payload(decode=True)
                            if payload:
                                charset = msg.get_content_charset() or 'utf-8'
                                try:
                                    html_content = payload.decode(charset)
                                except (UnicodeDecodeError, LookupError):
                                    html_content = payload.decode(
                                        'utf-8', errors='ignore')
                                mail.HTMLBody = html_content
                                mail.Body = _html_to_text(html_content)
                        else:
                            mail.Body = body

                    # Extrahiere Absender
                    if msg['From']:
                        from_header = msg['From']
                        decoded_from = email.header.decode_header(from_header)
                        from_parts = []
                        for part, encoding in decoded_from:
                            if isinstance(part, bytes):
                                if encoding:
                                    try:
                                        part = part.decode(encoding)
                                    except (UnicodeDecodeError, LookupError):
                                        part = part.decode(
                                            'utf-8', errors='ignore')
                                else:
                                    part = part.decode(
                                        'utf-8', errors='ignore')
                            from_parts.append(part)
                        from_decoded = ''.join(from_parts)

                        # Setze Absender
                        try:
                            if '<' in from_decoded and '>' in from_decoded:
                                sender_name = from_decoded.split(
                                    '<')[0].strip().strip('"')
                                sender_email = from_decoded.split(
                                    '<')[1].split('>')[0].strip()
                                mail.SenderEmailAddress = sender_email
                                mail.SenderName = sender_name
                            else:
                                mail.SenderEmailAddress = from_decoded.strip()
                                mail.SenderName = from_decoded.strip()
                        except Exception:
                            pass

                    # Extrahiere Empfänger
                    if msg['To']:
                        to_header = msg['To']
                        decoded_to = email.header.decode_header(to_header)
                        to_parts = []
                        for part, encoding in decoded_to:
                            if isinstance(part, bytes):
                                if encoding:
                                    try:
                                        part = part.decode(encoding)
                                    except (UnicodeDecodeError, LookupError):
                                        part = part.decode(
                                            'utf-8', errors='ignore')
                                else:
                                    part = part.decode(
                                        'utf-8', errors='ignore')
                            to_parts.append(part)
                        mail.To = ''.join(to_parts)

                    # Extrahiere Datum
                    if msg['Date']:
                        try:
                            sent_date = email.utils.parsedate_to_datetime(
                                msg['Date'])
                            mail.SentOn = sent_date
                        except Exception:
                            pass

                    # Füge Anhänge hinzu
                    for filename, attachment_data in attachments:
                        try:
                            import tempfile
                            import os

                            # Erstelle temporäre Datei für Anhang
                            with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{filename}") as temp_file:
                                temp_file.write(attachment_data)
                                temp_file_path = temp_file.name

                            try:
                                # Füge Anhang zur E-Mail hinzu
                                attachment = mail.Attachments.Add(
                                    temp_file_path)
                                attachment.DisplayName = filename
                                print(f"  Anhang hinzugefügt: {filename}")
                            except Exception as attach_error:
                                print(
                                    f"  Warnung: Anhang konnte nicht hinzugefügt werden: {attach_error}")
                            finally:
                                # Lösche temporäre Datei
                                try:
                                    os.unlink(temp_file_path)
                                except Exception:
                                    pass
                        except Exception as e:
                            print(f"  Fehler bei Anhang {filename}: {e}")

                    # Speichere E-Mail
                    mail.Save()
                    mail.Move(inbox)
                    successful_imports += 1

                    # Progress-Update
                    progress = 40 + int((idx / len(eml_files)) * 50)
                    self.progress.emit(progress)

                    if idx % 10 == 0:
                        self.status.emit(
                            f"Importiert: {idx}/{len(eml_files)} E-Mails")

                except Exception as e:
                    failed_imports += 1
                    print(f"Fehler beim Importieren von {eml_file.name}: {e}")

            self.status.emit(
                f"Import abgeschlossen: {successful_imports} erfolgreich, {failed_imports} fehlgeschlagen")
            self.progress.emit(95)

            # Schließe PST-Datei
            mapi.RemoveStore(new_store)

            return pst_output_path

        finally:
            try:
                if outlook:
                    outlook.Quit()
            except Exception:
                pass
            pythoncom.CoUninitialize()


# ---------------------------------------------------------------------------
# ConverterThread für OLM → EML Export
# ---------------------------------------------------------------------------

class ConverterThread(QThread):
    overallProgress = Signal(int)
    currentFile = Signal(str)
    finishedWithErrors = Signal(str)
    pstCreated = Signal(str)
    statusUpdate = Signal(str)

    def __init__(self, olm_paths):
        super().__init__()
        self._olm_paths = list(olm_paths)
        self._stop = threading.Event()

    def run(self):
        try:
            total = len(self._olm_paths)
            for idx, olm in enumerate(self._olm_paths, 1):
                if self._stop.is_set():
                    break
                olm_path = Path(olm)
                self.currentFile.emit(olm_path.name)
                try:
                    output_path = self._convert_olm(olm_path)
                    self.pstCreated.emit(str(output_path))
                except Exception as exc:
                    self.finishedWithErrors.emit(
                        f"{olm_path.name}: {exc}\n{traceback.format_exc()}")
                self.overallProgress.emit(int(idx / total * 100))
        finally:
            pass

    def stop(self):
        """Stoppt den Thread."""
        self._stop.set()

    # ------------------------------------------------------------------

    def _convert_olm(self, olm_path: Path):
        # Verwende temporäres Verzeichnis für olmConvert
        import tempfile
        olm_extract_dir = Path(tempfile.mkdtemp(prefix="olm_extract_"))

        # Erstelle einen organisierten Ausgabeordner auf dem Desktop
        desktop_path = Path.home() / "Desktop"
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = desktop_path / f"EML_Export_{olm_path.stem}_{timestamp}"
        output_dir.mkdir(exist_ok=True)

        try:
            # Extrahiere die OLM-Datei zu .eml-Dateien
            self.statusUpdate.emit("Extrahiere E-Mails aus OLM-Datei...")
            print(f"Starte convertOLM: {olm_path} -> {olm_extract_dir}")

            try:
                convertOLM(str(olm_path), str(olm_extract_dir),
                           noAttachments=False, format="eml")
                print("convertOLM abgeschlossen")
            except Exception as e:
                print(f"convertOLM Fehler: {e}")
                raise

            # Sammle alle .eml-Dateien
            eml_files = list(olm_extract_dir.rglob("*.eml"))
            total_emails = len(eml_files)
            self.statusUpdate.emit(f"Gefunden: {total_emails} E-Mail-Dateien")
            print(f"Gefunden: {total_emails} E-Mail-Dateien")

            successful_copies = 0
            failed_copies = 0

            # Kopiere und organisiere die .eml-Dateien
            for idx, eml_file in enumerate(eml_files):
                if self._stop.is_set():
                    break

                # Fortschrittsupdate
                if idx % 10 == 0 or idx == total_emails - 1:
                    progress_msg = f"Kopiere E-Mail {idx + 1}/{total_emails} ({successful_copies} erfolgreich, {failed_copies} fehlgeschlagen)"
                    self.statusUpdate.emit(progress_msg)

                try:
                    # Erstelle einen sinnvollen Dateinamen für die .eml-Datei
                    with open(eml_file, 'rb') as f:
                        eml_content = f.read()

                    # Parse die E-Mail um einen besseren Dateinamen zu erstellen
                    msg = email.message_from_bytes(eml_content)

                    # Extrahiere Betreff für Dateinamen
                    subject = "Email"
                    if msg['Subject']:
                        decoded_subject = email.header.decode_header(
                            msg['Subject'])
                        subject_parts = []
                        for part, encoding in decoded_subject:
                            if isinstance(part, bytes):
                                if encoding:
                                    try:
                                        part = part.decode(encoding)
                                    except (UnicodeDecodeError, LookupError):
                                        part = part.decode(
                                            'utf-8', errors='ignore')
                                else:
                                    part = part.decode(
                                        'utf-8', errors='ignore')
                            subject_parts.append(part)
                        subject = ''.join(subject_parts)

                    # Extrahiere Datum für Dateinamen
                    date_str = "unknown_date"
                    if msg['Date']:
                        try:
                            sent_date = email.utils.parsedate_to_datetime(
                                msg['Date'])
                            date_str = sent_date.strftime("%Y%m%d_%H%M%S")
                        except Exception:
                            pass

                    # Erstelle sicheren Dateinamen
                    safe_subject = _sanitize_filename(subject)
                    new_filename = f"{date_str}_{safe_subject}.eml"

                    # Kopiere die .eml-Datei in den Ausgabeordner
                    target_path = output_dir / new_filename

                    # Stelle sicher, dass der Dateiname eindeutig ist
                    counter = 1
                    while target_path.exists():
                        name_part = f"{date_str}_{safe_subject}_{counter}.eml"
                        target_path = output_dir / name_part
                        counter += 1

                    # Kopiere die Datei
                    import shutil
                    shutil.copy2(eml_file, target_path)
                    successful_copies += 1

                    print(f"Kopiert: {new_filename}")

                except Exception as e:
                    failed_copies += 1
                    print(f"Fehler beim Kopieren von {eml_file.name}: {e}")

            # Erstelle eine Anleitung für den Benutzer
            instructions_file = output_dir / "ANLEITUNG.txt"
            instructions_text = f"""
EML-Export abgeschlossen!

Exportiert: {successful_copies} E-Mails
Fehler: {failed_copies} E-Mails

IMPORT IN OUTLOOK:

1. Öffnen Sie Microsoft Outlook
2. Gehen Sie zu Datei > Öffnen und Exportieren > Importieren/Exportieren
3. Wählen Sie "Internetmail-Nachrichten importieren"
4. Wählen Sie "Outlook Express" oder "Windows Mail"
5. Navigieren Sie zu diesem Ordner: {output_dir}
6. Wählen Sie die .eml-Dateien aus, die Sie importieren möchten

ALTERNATIVE:
- Sie können .eml-Dateien auch einzeln per Doppelklick öffnen
- Oder per Drag & Drop in einen Outlook-Ordner ziehen

Exportiert am: {datetime.now().strftime("%d.%m.%Y %H:%M:%S")}
Ursprüngliche OLM-Datei: {olm_path.name}
"""

            with open(instructions_file, 'w', encoding='utf-8') as f:
                f.write(instructions_text)

            final_msg = f"EML-Export abgeschlossen: {successful_copies} Dateien exportiert nach {output_dir}"
            self.statusUpdate.emit(final_msg)
            print(final_msg)

            return output_dir
        finally:
            # Räume das temporäre olmConvert-Verzeichnis auf
            shutil.rmtree(olm_extract_dir, ignore_errors=True)


# ---------------------------------------------------------------------------
# GUI
# ---------------------------------------------------------------------------

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("OLM → EML/PST Converter")
        self.resize(650, 500)
        self._had_error = False

        # Widgets
        self.list_widget = QListWidget()
        self.add_btn = QPushButton("OLM-Dateien hinzufügen…")
        self.remove_btn = QPushButton("Markierte entfernen")
        self.start_btn = QPushButton("Zu EML exportieren")

        # Neue PST-Funktionen
        self.separator = QLabel("─" * 50)
        self.separator.setAlignment(Qt.AlignCenter)
        self.pst_label = QLabel("PST-Datei aus EML-Ordner erstellen:")
        self.pst_label.setStyleSheet("font-weight: bold;")
        self.select_eml_btn = QPushButton("EML-Ordner auswählen…")
        self.create_pst_btn = QPushButton("PST-Datei erstellen")
        self.create_pst_btn.setEnabled(False)

        self.progress = QProgressBar(maximum=100)
        self.status = QLabel("Bereit")

        # Layout
        layout = QVBoxLayout()
        layout.addWidget(QLabel("1. OLM zu EML konvertieren:"))
        layout.addWidget(self.list_widget)
        layout.addWidget(self.add_btn)
        layout.addWidget(self.remove_btn)
        layout.addWidget(self.start_btn)
        layout.addWidget(self.separator)
        layout.addWidget(self.pst_label)
        layout.addWidget(self.select_eml_btn)
        layout.addWidget(self.create_pst_btn)
        layout.addWidget(self.progress)
        layout.addWidget(self.status)

        container = QWidget()
        container.setLayout(layout)
        self.setCentralWidget(container)

        # Verbindungen
        self.add_btn.clicked.connect(self._select_files)
        self.remove_btn.clicked.connect(self._remove_selected)
        self.start_btn.clicked.connect(self._start_conversion)
        self.select_eml_btn.clicked.connect(self._select_eml_folder)
        self.create_pst_btn.clicked.connect(self._create_pst)

        # Für PST-Erstellung
        self.selected_eml_folder = None

    def _select_files(self):
        files, _ = QFileDialog.getOpenFileNames(
            self, "OLM‑Dateien wählen", "", "Outlook for Mac‑Archive (*.olm)")
        for f in files:
            if not any(f == self.list_widget.item(i).text() for i in range(self.list_widget.count())):
                self.list_widget.addItem(QListWidgetItem(f))

    def _remove_selected(self):
        for itm in self.list_widget.selectedItems():
            self.list_widget.takeItem(self.list_widget.row(itm))

    def _select_eml_folder(self):
        """Wählt einen Ordner mit .eml-Dateien aus."""
        folder = QFileDialog.getExistingDirectory(
            self, "EML-Ordner auswählen", str(Path.home() / "Desktop"))
        if folder:
            eml_folder = Path(folder)
            eml_files = list(eml_folder.glob("*.eml"))
            if eml_files:
                self.selected_eml_folder = eml_folder
                self.create_pst_btn.setEnabled(True)
                self.create_pst_btn.setText(
                    f"PST erstellen ({len(eml_files)} E-Mails)")
                self.status.setText(
                    f"EML-Ordner ausgewählt: {len(eml_files)} E-Mails gefunden")
            else:
                QMessageBox.warning(
                    self, "Keine EML-Dateien",
                    f"Im ausgewählten Ordner wurden keine .eml-Dateien gefunden:\n{folder}")

    def _create_pst(self):
        """Erstellt eine PST-Datei aus dem ausgewählten EML-Ordner."""
        if not self.selected_eml_folder:
            return

        # Frage nach PST-Ausgabepfad
        suggested_name = f"{self.selected_eml_folder.name}.pst"
        pst_path, _ = QFileDialog.getSaveFileName(
            self, "PST-Datei speichern unter",
            str(self.selected_eml_folder.parent / suggested_name),
            "Outlook-Datendateien (*.pst)")

        if not pst_path:
            return

        # Starte PST-Erstellung in separatem Thread
        self.progress.setValue(0)
        self.status.setText("Erstelle PST-Datei...")
        self.create_pst_btn.setEnabled(False)
        self.start_btn.setEnabled(False)

        self.pst_worker = PSTCreatorThread(self.selected_eml_folder, pst_path)
        self.pst_worker.progress.connect(self.progress.setValue)
        self.pst_worker.status.connect(self.status.setText)
        self.pst_worker.finished_success.connect(self._on_pst_created)
        self.pst_worker.finished_error.connect(self._on_pst_error)
        self.pst_worker.start()

    def _on_pst_created(self, pst_path):
        """Wird aufgerufen wenn PST-Erstellung erfolgreich war."""
        self.progress.setValue(100)
        self.status.setText(f"PST-Datei erstellt: {pst_path}")
        self.create_pst_btn.setEnabled(True)
        self.start_btn.setEnabled(True)
        QMessageBox.information(
            self, "PST-Datei erstellt",
            f"Die PST-Datei wurde erfolgreich erstellt:\n{pst_path}")

    def _on_pst_error(self, error_msg):
        """Wird aufgerufen wenn PST-Erstellung fehlgeschlagen ist."""
        self.progress.setValue(0)
        self.status.setText("Fehler bei PST-Erstellung")
        self.create_pst_btn.setEnabled(True)
        self.start_btn.setEnabled(True)
        QMessageBox.critical(
            self, "Fehler bei PST-Erstellung", error_msg)

    def _start_conversion(self):
        if self.list_widget.count() == 0:
            QMessageBox.warning(
                self, "Keine Dateien", "Bitte zuerst mindestens eine .olm‑Datei hinzufügen.")
            return
        paths = [self.list_widget.item(i).text()
                 for i in range(self.list_widget.count())]
        self.progress.setValue(0)
        self.status.setText("Starte…")
        self.start_btn.setEnabled(False)
        self._had_error = False
        self.worker = ConverterThread(paths)
        self.worker.overallProgress.connect(self.progress.setValue)
        self.worker.currentFile.connect(
            lambda n: self.status.setText(f"Verarbeite: {n}"))
        self.worker.statusUpdate.connect(
            self.status.setText)  # Neues Signal verbinden
        self.worker.finishedWithErrors.connect(self._on_error)
        self.worker.pstCreated.connect(self._on_pst)
        self.worker.finished.connect(self._on_finish)
        self.worker.start()

    def _on_pst(self, path):
        self.status.setText(f"Exportiert nach: {path}")
        QMessageBox.information(
            self, "EML-Export abgeschlossen",
            f"Die E-Mails wurden exportiert nach:\n{path}\n\n"
            f"Öffnen Sie die ANLEITUNG.txt-Datei im Ordner für weitere Informationen zum Import in Outlook.")

    def _on_error(self, msg):
        self._had_error = True
        self.status.setText("Fehler")
        QMessageBox.critical(self, "Fehler während des Exports", msg)

    def _on_finish(self):
        self.status.setText("Fertig!")
        self.start_btn.setEnabled(True)
        if not self._had_error:
            QMessageBox.information(self, "Export abgeschlossen",
                                    "Alle ausgewählten OLM‑Dateien wurden erfolgreich zu EML-Dateien exportiert.")


# ---------------------------------------------------------------------------
# Entry‑Point
# ---------------------------------------------------------------------------

def main():
    warnings.simplefilter("default")
    _set_dpi_aware()
    QCoreApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app = QApplication(sys.argv)
    wnd = MainWindow()
    wnd.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
