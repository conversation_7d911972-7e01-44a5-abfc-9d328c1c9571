from pathlib import Path
from olmConvert import convertOLM
import tempfile
import shutil
import sys
import pprint

olm_path = Path(r"dist\Outlook für Mac-Archiv.olm")
out_dir = Path(tempfile.mkdtemp())
convertOLM(str(olm_path), str(out_dir), noAttachments=False, format="eml")
eml_files = list(out_dir.rglob("*.eml"))
print("Extrahierte .eml:", len(eml_files))
print("Beispiel-Dateien:", [str(p) for p in eml_files[:5]])
